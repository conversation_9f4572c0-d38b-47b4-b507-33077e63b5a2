import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

const ProjectImageSwiper = ({ images, title, isNDA = false, onImageInteraction }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Handle escape key to close fullscreen
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isFullscreen]);

  const openFullscreen = (imageIndex) => {
    setCurrentImageIndex(imageIndex);
    setIsFullscreen(true);
    // Track image interaction
    if (onImageInteraction) {
      onImageInteraction(imageIndex);
    }
  };

  const closeFullscreen = () => {
    setIsFullscreen(false);
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      closeFullscreen();
    }
  };



  // TEMPORARY: Simple image display to test positioning
  return (
    <>
      {/* --- Simple Image Display for Testing --- */}
      <div className="project-image-swiper" style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', background: 'red', zIndex: 1 }}>
        <img
          src={images[0]}
          alt={`${title} - View 1`}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 1
          }}
        />
        {/* Test indicator */}
        <div style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: 'yellow',
          color: 'black',
          padding: '5px',
          zIndex: 20
        }}>
          SWIPE
        </div>
      </div>

      {/* Fullscreen Modal - Rendered as Portal */}
      {isFullscreen && createPortal(
        <div className="fullscreen-modal" onClick={handleBackdropClick}>
          <div className="fullscreen-content">
            <button
              className="fullscreen-close"
              onClick={closeFullscreen}
              title="Close (Esc)"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <img
              src={images[currentImageIndex]}
              alt={`${title} - Fullscreen View`}
              className="fullscreen-image"
            />
            {images.length > 1 && (
              <div className="fullscreen-navigation">
                <button
                  className="fullscreen-nav-btn fullscreen-prev"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentImageIndex((prev) =>
                      prev === 0 ? images.length - 1 : prev - 1
                    );
                  }}
                  title="Previous Image"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <button
                  className="fullscreen-nav-btn fullscreen-next"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentImageIndex((prev) =>
                      prev === images.length - 1 ? 0 : prev + 1
                    );
                  }}
                  title="Next Image"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            )}
            <div className="fullscreen-counter">
              {currentImageIndex + 1} / {images.length}
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default ProjectImageSwiper;
