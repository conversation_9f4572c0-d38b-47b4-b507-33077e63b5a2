/* Project Image Swiper Styles */
.project-image-swiper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 15px 15px 0 0;
  display: block;
  background: transparent;
}

.project-swiper {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  overflow: hidden;
}

/* Override default Swiper styles */
.project-swiper .swiper-wrapper {
  width: 100% !important;
  height: 100% !important;
  position: relative;
}

.project-swiper .swiper-slide {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  display: block;
}

.swiper-slide-content {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  cursor: default;
  display: block !important;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}

.swiper-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  transition: transform 0.3s ease;
  cursor: default;
  display: block !important;
  position: relative;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}

.project-card:hover .swiper-image {
  transform: scale(1.05);
}

/* Custom Navigation Buttons */
.swiper-button-prev-custom,
.swiper-button-next-custom {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  opacity: 0;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.swiper-button-prev-custom {
  left: 15px;
}

.swiper-button-next-custom {
  right: 15px;
}

.project-card:hover .swiper-button-prev-custom,
.project-card:hover .swiper-button-next-custom {
  opacity: 1;
}

.swiper-button-prev-custom:hover,
.swiper-button-next-custom:hover {
  background: rgba(75, 0, 130, 0.8);
  border-color: #FF2D55;
  transform: translateY(-50%) scale(1.1);
}

.swiper-button-prev-custom span,
.swiper-button-next-custom span {
  color: #FFFFFF;
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
}

/* Custom Pagination */
.project-swiper .swiper-pagination {
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
}

.project-swiper .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;
  width: 8px;
  height: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
}

.project-swiper .swiper-pagination-bullet-active {
  background: #FF2D55;
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(255, 45, 85, 0.5);
}

/* Swipe Indicator */
.swipe-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 20;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.project-card:hover .swipe-indicator {
  opacity: 1;
  transform: translateY(0);
}





.swipe-text {
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.swipe-animation {
  display: flex;
  gap: 3px;
  align-items: center;
}

.swipe-dot {
  width: 4px;
  height: 4px;
  background: #FF2D55;
  border-radius: 50%;
  animation: swipeAnimation 2s infinite;
}

.swipe-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.swipe-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes swipeAnimation {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Fullscreen Icon */
.fullscreen-icon {
  position: absolute;
  bottom: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 25;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
}

.project-card:hover .fullscreen-icon {
  opacity: 1;
  transform: translateY(0);
}

.fullscreen-icon:hover {
  background: rgba(75, 0, 130, 0.8);
  transform: translateY(0) scale(1.1);
  box-shadow: 0 5px 15px rgba(75, 0, 130, 0.4);
}

/* Fullscreen Modal */
.fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  animation: fadeIn 0.3s ease;
}

.fullscreen-content {
  position: relative;
  max-width: 95vw;
  max-height: 95vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 10px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.fullscreen-close {
  position: absolute;
  top: -50px;
  right: -10px;
  width: 44px;
  height: 44px;
  background: rgba(255, 45, 85, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100001;
  transition: all 0.3s ease;
  color: #FFFFFF;
}

.fullscreen-close:hover {
  background: rgba(255, 45, 85, 1);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(255, 45, 85, 0.4);
}

.fullscreen-navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  padding: 0 20px;
}

.fullscreen-nav-btn {
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #FFFFFF;
  pointer-events: all;
}

.fullscreen-nav-btn:hover {
  background: rgba(75, 0, 130, 0.8);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(75, 0, 130, 0.4);
}

.fullscreen-counter {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  /* Fix main swiper container positioning */
  .project-image-swiper {
    position: relative;
    z-index: 1;
  }

  .project-swiper {
    position: relative;
    z-index: 1;
  }

  .swiper-slide-content {
    position: relative;
    z-index: 1;
  }

  .swiper-image {
    position: relative;
    z-index: 1;
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .fullscreen-icon {
    width: 35px;
    height: 35px;
    bottom: 10px;
    right: 10px;
    z-index: 25;
  }

  .fullscreen-close {
    top: -40px;
    right: -5px;
    width: 40px;
    height: 40px;
  }

  .fullscreen-navigation {
    padding: 0 15px;
  }

  .fullscreen-nav-btn {
    width: 45px;
    height: 45px;
  }

  .fullscreen-counter {
    bottom: -40px;
    font-size: 12px;
    padding: 6px 12px;
  }

  .fullscreen-content {
    max-width: 98vw;
    max-height: 98vh;
  }

  .swiper-button-prev-custom,
  .swiper-button-next-custom {
    width: 35px;
    height: 35px;
    z-index: 12;
    position: absolute;
  }

  .swiper-button-prev-custom {
    left: 10px;
  }

  .swiper-button-next-custom {
    right: 10px;
  }

  .swiper-button-prev-custom span,
  .swiper-button-next-custom span {
    font-size: 18px;
  }

  .swipe-indicator {
    top: 10px;
    right: 10px;
    padding: 6px 10px;
    z-index: 20;
  }

  .swipe-text {
    font-size: 11px;
  }

  .project-swiper .swiper-pagination {
    bottom: 10px;
    z-index: 12;
  }
}

@media (max-width: 480px) {
  /* Ensure proper stacking for very small screens */
  .project-image-swiper {
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .swiper-image {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .fullscreen-icon {
    width: 32px;
    height: 32px;
    bottom: 8px;
    right: 8px;
    z-index: 25;
  }

  .fullscreen-close {
    top: -35px;
    right: 0;
    width: 36px;
    height: 36px;
  }

  .fullscreen-nav-btn {
    width: 40px;
    height: 40px;
  }

  .fullscreen-navigation {
    padding: 0 10px;
  }

  .swiper-button-prev-custom,
  .swiper-button-next-custom {
    width: 30px;
    height: 30px;
    z-index: 12;
    position: absolute;
  }

  .swiper-button-prev-custom span,
  .swiper-button-next-custom span {
    font-size: 16px;
  }

  .project-swiper .swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    margin: 0 3px;
  }

  .project-swiper .swiper-pagination {
    z-index: 12;
  }

  .swipe-indicator {
    z-index: 20;
  }
}

/* Touch/Mobile specific styles */
@media (hover: none) and (pointer: coarse) {
  /* Ensure all interactive elements are visible on touch devices */
  .swiper-button-prev-custom,
  .swiper-button-next-custom,
  .swipe-indicator,
  .fullscreen-icon {
    opacity: 1;
  }

  .swipe-indicator,
  .fullscreen-icon {
    transform: translateY(0);
  }

  /* Fix image display on touch devices */
  .project-image-swiper {
    position: relative;
    z-index: 1;
  }

  .swiper-image {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .swiper-slide-content {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
  }
}
